# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

BuddyDocs (Firestarter) is a RAG-powered AI chatbot system that crawls websites and creates searchable knowledge bases with OpenAI-compatible API endpoints. Built with Next.js 15, it uses Firecrawl for web scraping, Upstash for vector search, and supports multiple AI providers (OpenAI, Anthropic, Groq).

## Essential Commands

```bash
# Development
npm run dev           # Start dev server with Turbopack on localhost:3000
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# TypeScript Check (important before commits)
npx tsc --noEmit     # Check TypeScript types
```

## Architecture Overview

### API Routes Structure
- `/api/firestarter/create` - Creates new chatbot by crawling website
- `/api/firestarter/query` - Queries existing chatbot with RAG pipeline
- `/api/firestarter/debug` - Debug endpoint for testing
- `/api/v1/chat/completions` - OpenAI-compatible endpoint
- `/api/scrape` - Direct web scraping endpoint
- `/api/indexes` - Manage chatbot indexes
- `/api/check-env` - Environment validation

### Key Components
1. **RAG Pipeline**: 
   - Web crawling → Markdown conversion → Vector embedding → Semantic search → Context-aware LLM response
   - Each chatbot gets a unique namespace (e.g., `firecrawl-example-com-12345`)

2. **Storage Layer**:
   - Upstash Search for vector embeddings
   - Redis for metadata (optional)
   - Local storage fallback

3. **AI Provider Priority**:
   - OpenAI (GPT-4o) → Anthropic (Claude 3.5 Sonnet) → Groq
   - Configured in `firestarter.config.ts`

## Configuration

Main configuration in `firestarter.config.ts`:
- AI model selection based on available API keys
- Rate limiting with Upstash Redis
- Crawling limits (10-100 pages)
- Search parameters (max results, context length)

## Environment Variables

Required:
```
FIRECRAWL_API_KEY=
UPSTASH_SEARCH_REST_URL=
UPSTASH_SEARCH_REST_TOKEN=
```

At least one AI provider:
```
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
GROQ_API_KEY=
```

Optional:
```
UPSTASH_REDIS_REST_URL=
UPSTASH_REDIS_REST_TOKEN=
DISABLE_CHATBOT_CREATION=true
```

## Development Patterns

1. **TypeScript Strict Mode**: Always enabled, no `any` types
2. **Path Aliases**: Use `@/` for imports from root
3. **Component Library**: Radix UI + shadcn/ui components in `/components/ui`
4. **Styling**: Tailwind CSS with custom animations
5. **Error Handling**: Rate limiting, proper error responses
6. **Security**: CORS headers, API key validation

## Key Libraries
- **AI SDK**: Vercel AI SDK for streaming responses
- **Web Scraping**: @mendable/firecrawl-js
- **Vector Search**: @upstash/search
- **UI Components**: Radix UI primitives
- **Forms**: react-hook-form with zod validation
- **Notifications**: sonner for toasts

## Testing Approach

Before making changes:
1. Check TypeScript compilation: `npx tsc --noEmit`
2. Run linter: `npm run lint`
3. Test API endpoints in `/app/debug/page.tsx`
4. Verify rate limiting is working properly

## OpenAI-Compatible API

The `/api/v1/chat/completions` endpoint supports:
- Standard OpenAI SDK format
- Model format: `firecrawl-<domain>-<id>`
- Streaming responses
- Direct Groq/OpenAI passthrough with headers